"""
BRITS (Bidirectional Recurrent Imputation Time Series) Model Implementation
Specialized for temporal dependencies in well log data

This module implements the BRITS model for well log imputation, leveraging
bidirectional RNN architecture for temporal pattern modeling.
"""

import torch
import numpy as np
from typing import Dict, Any, Optional
import warnings

# Import PyPOTS components with error handling
try:
    from pypots.imputation import BRITS
    from pypots.optim import Adam
    PYPOTS_AVAILABLE = True
except ImportError as e:
    PYPOTS_AVAILABLE = False
    warnings.warn(f"PyPOTS not available: {e}")
    BRITS = None
    Adam = None

from .base_model import BaseAdvancedModel

class BRITSModel(BaseAdvancedModel):
    """
    BRITS model wrapper for well log imputation.
    Leverages bidirectional RNN for temporal pattern modeling.
    
    BRITS (Bidirectional Recurrent Imputation Time Series) is specifically
    designed for time series imputation using bidirectional RNNs with
    temporal decay factors and feature correlation modeling.
    """

    def __init__(self, n_features=4, sequence_len=64, rnn_hidden_size=128,
                 epochs=50, batch_size=32, learning_rate=1e-3, **kwargs):
        """
        Initialize BRITS model.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            rnn_hidden_size: Hidden size for RNN layers
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)
        
        if not PYPOTS_AVAILABLE:
            raise ImportError("PyPOTS is required for BRITS model. Please install with: pip install pypots")
        
        self.rnn_hidden_size = rnn_hidden_size
        
        # Validate parameters
        self._validate_parameters()
        
        print(f"🎯 BRITS Model Configuration:")
        print(f"   - RNN hidden size: {rnn_hidden_size}")
        print(f"   - Bidirectional: True")
        print(f"   - Temporal decay: Enabled")
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.rnn_hidden_size < 16:
            print("⚠️ Warning: rnn_hidden_size < 16 may limit model capacity")
        
        if self.sequence_len < 8:
            print("⚠️ Warning: sequence_len < 8 may not capture temporal patterns effectively")
            
        if self.rnn_hidden_size > 512:
            print(f"⚠️ Warning: rnn_hidden_size={self.rnn_hidden_size} may be too large (recommended: 64-512)")

    def _initialize_model(self) -> None:
        """Initialize the PyPOTS BRITS model."""
        try:
            print(f"🔧 Initializing BRITS model...")
            
            self.model = BRITS(
                n_steps=self.sequence_len,
                n_features=self.n_features,
                rnn_hidden_size=self.rnn_hidden_size,
                batch_size=self.batch_size,
                epochs=self.epochs,
                patience=15,  # Early stopping patience
                optimizer=Adam(lr=self.learning_rate),
                device='cpu',  # Will auto-detect GPU if available
                saving_path=None,
                model_saving_strategy=None
            )
            
            print(f"✅ BRITS model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"❌ Failed to initialize BRITS model: {e}")
            raise RuntimeError(f"BRITS model initialization failed: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in PyPOTS format for BRITS."""
        return self._prepare_pypots_data(data, truth_data)
    
    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        # Estimate parameters for bidirectional RNN
        rnn_params = (
            # Forward RNN (LSTM gates: input, forget, output, cell)
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Backward RNN  
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Output layers and decay mechanisms
            2 * self.rnn_hidden_size * self.n_features +
            # Additional BRITS-specific parameters (decay, imputation layers)
            self.n_features * self.rnn_hidden_size * 2
        )
        
        return rnn_params
    
    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32
        activation_memory = (
            self.batch_size * self.sequence_len * self.rnn_hidden_size * 
            4 / (1024 * 1024)  # Bidirectional + hidden states
        )
        return param_memory + activation_memory
    
    def get_temporal_patterns(self, data: torch.Tensor) -> Optional[Dict[str, np.ndarray]]:
        """
        Extract temporal patterns learned by the model.
        
        Args:
            data: Input data tensor
            
        Returns:
            Dictionary with temporal pattern information or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting temporal patterns")
            return None
            
        try:
            # This would require modification of PyPOTS BRITS to expose hidden states
            # For now, return None and implement in future versions
            print("ℹ️ Temporal pattern extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract temporal patterns: {e}")
            return None
    
    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'rnn_hidden_size': self.rnn_hidden_size,
            'bidirectional': True,
            'complexity_score': 2,  # Medium complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'medium',
            'performance_tier': 'high'
        }
    
    def get_hyperparameter_ranges(self) -> Dict[str, Dict[str, Any]]:
        """Get recommended hyperparameter ranges for optimization."""
        return {
            'rnn_hidden_size': {'min': 32, 'max': 512, 'default': 128, 'type': 'int', 'step': 32},
            'learning_rate': {'min': 1e-5, 'max': 1e-2, 'default': 1e-3, 'type': 'float'},
            'batch_size': {'min': 8, 'max': 128, 'default': 32, 'type': 'int'},
            'epochs': {'min': 10, 'max': 200, 'default': 50, 'type': 'int'}
        }
    
    def optimize_for_dataset(self, data_shape: tuple, missing_rate: float = 0.3) -> Dict[str, Any]:
        """
        Suggest optimal hyperparameters based on dataset characteristics.
        
        Args:
            data_shape: Shape of the dataset (batch, sequence, features)
            missing_rate: Proportion of missing values
            
        Returns:
            Dictionary with suggested hyperparameters
        """
        batch_size, seq_len, n_feat = data_shape
        
        # Adjust RNN size based on sequence length and features
        if seq_len < 32:
            suggested_hidden_size = 64
        elif seq_len < 64:
            suggested_hidden_size = 128
        else:
            suggested_hidden_size = 256
            
        # Adjust based on number of features
        suggested_hidden_size = max(suggested_hidden_size, n_feat * 16)
        
        # Adjust based on missing rate
        if missing_rate > 0.5:
            suggested_epochs = 100  # More epochs for high missing rates
        else:
            suggested_epochs = 50
            
        # Adjust batch size based on available data
        if batch_size < 100:
            suggested_batch_size = min(16, batch_size // 2)
        else:
            suggested_batch_size = 32
            
        return {
            'rnn_hidden_size': min(suggested_hidden_size, 512),
            'epochs': suggested_epochs,
            'batch_size': suggested_batch_size,
            'learning_rate': 1e-3
        }
    
    def __repr__(self) -> str:
        """Enhanced string representation of the BRITS model."""
        status = "fitted" if self.is_fitted else "unfitted"
        return (f"BRITSModel("
                f"n_features={self.n_features}, "
                f"sequence_len={self.sequence_len}, "
                f"rnn_hidden_size={self.rnn_hidden_size}, "
                f"status={status})")

# Utility functions for BRITS model
def create_brits_model_from_config(config: Dict[str, Any]) -> BRITSModel:
    """
    Create a BRITS model from configuration dictionary.
    
    Args:
        config: Configuration dictionary with model parameters
        
    Returns:
        Configured BRITSModel instance
    """
    return BRITSModel(**config)

def validate_brits_config(config: Dict[str, Any]) -> bool:
    """
    Validate BRITS model configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        bool: True if configuration is valid
    """
    required_keys = ['n_features', 'sequence_len']
    for key in required_keys:
        if key not in config:
            print(f"❌ Missing required configuration key: {key}")
            return False
    
    # Validate RNN hidden size
    rnn_hidden_size = config.get('rnn_hidden_size', 128)
    if rnn_hidden_size < 16 or rnn_hidden_size > 1024:
        print(f"⚠️ Warning: rnn_hidden_size={rnn_hidden_size} may not be optimal (recommended: 16-1024)")
    
    print("✅ BRITS configuration is valid")
    return True
